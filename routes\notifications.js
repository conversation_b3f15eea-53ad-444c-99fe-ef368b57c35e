const express = require('express');
const router = express.Router();
const Notification = require('../models/Notification');
const Permission = require('../models/Permission');
const notificationService = require('../services/notificationService');

// Middleware to check if user is admin
const requireAdmin = Permission.requireRole('admin');

// Middleware to check if user is authenticated
const requireAuth = (req, res, next) => {
  if (!req.session.userId) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
  }
  next();
};

// Get notifications for regular users
router.get('/user', requireAuth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      type,
      category,
      priority,
      is_read
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);

    const options = {
      target_user_id: req.session.userId, // User-specific notifications
      limit: parseInt(limit),
      offset,
      ...(type && { type }),
      ...(category && { category }),
      ...(priority && { priority }),
      ...(is_read !== undefined && { is_read: is_read === 'true' })
    };

    const notifications = await Notification.findAll(options);
    const unreadCount = await Notification.getUnreadCount(req.session.userId);

    res.json({
      success: true,
      notifications,
      unreadCount,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        hasMore: notifications.length === parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching user notifications:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch notifications'
    });
  }
});

// Get unread count for regular users
router.get('/user/unread-count', requireAuth, async (req, res) => {
  try {
    const count = await Notification.getUnreadCount(req.session.userId);
    res.json({ success: true, count });
  } catch (error) {
    console.error('Error fetching user unread count:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch unread count'
    });
  }
});

// Mark user notification as read
router.post('/user/:id/mark-read', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;

    // Verify the notification belongs to the user
    const notification = await Notification.findById(id);
    if (!notification || notification.target_user_id !== req.session.userId) {
      return res.status(404).json({
        success: false,
        error: 'Notification not found'
      });
    }

    const success = await Notification.markAsRead(id);

    if (success) {
      // Emit real-time update to the user
      notificationService.emitToUser(req.session.userId, 'notification:updated', {
        id,
        isRead: true
      });

      res.json({ success: true });
    } else {
      res.status(404).json({
        success: false,
        error: 'Notification not found'
      });
    }
  } catch (error) {
    console.error('Error marking user notification as read:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to mark notification as read'
    });
  }
});

// Mark all user notifications as read
router.post('/user/mark-all-read', requireAuth, async (req, res) => {
  try {
    const count = await Notification.markAllAsRead(req.session.userId);

    // Emit real-time update to the user
    notificationService.emitToUser(req.session.userId, 'notification:allMarkedRead');

    res.json({
      success: true,
      markedCount: count
    });
  } catch (error) {
    console.error('Error marking all user notifications as read:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to mark all notifications as read'
    });
  }
});

// Get all notifications for admin
router.get('/admin', async (req, res) => {
  try {
    console.log('=== ADMIN ENDPOINT CALLED ===');
    console.log('Session:', req.session);
    console.log('User ID:', req.session?.userId);
    console.log('User Role:', req.session?.userRole);

    const {
      page = 1,
      limit = 20,
      type,
      category,
      priority,
      is_read
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);

    const options = {
      target_user_id: null, // Admin notifications only
      limit: parseInt(limit),
      offset,
      ...(type && { type }),
      ...(category && { category }),
      ...(priority && { priority }),
      ...(is_read !== undefined && { is_read: is_read === 'true' })
    };

    console.log('Query options:', options);

    const notifications = await Notification.findAll(options);
    const unreadCount = await Notification.getUnreadCount();
    const rawStats = await Notification.getStats();

    console.log('Found notifications:', notifications.length);
    console.log('Unread count:', unreadCount);
    console.log('Raw stats length:', rawStats.length);

    // Process stats into the expected format
    const stats = {
      total: 0,
      byType: {},
      byCategory: {},
      byPriority: {}
    };

    // Calculate total notifications
    rawStats.forEach(stat => {
      stats.total += stat.count;

      // Group by type
      if (!stats.byType[stat.type]) {
        stats.byType[stat.type] = { total: 0, unread: 0 };
      }
      stats.byType[stat.type].total += stat.count;
      stats.byType[stat.type].unread += stat.unread_count;

      // Group by category
      if (!stats.byCategory[stat.category]) {
        stats.byCategory[stat.category] = { total: 0, unread: 0 };
      }
      stats.byCategory[stat.category].total += stat.count;
      stats.byCategory[stat.category].unread += stat.unread_count;

      // Group by priority
      if (!stats.byPriority[stat.priority]) {
        stats.byPriority[stat.priority] = { total: 0, unread: 0 };
      }
      stats.byPriority[stat.priority].total += stat.count;
      stats.byPriority[stat.priority].unread += stat.unread_count;
    });

    console.log('Processed stats:', stats);

    res.json({
      success: true,
      notifications,
      unreadCount,
      stats,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        hasMore: notifications.length === parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching admin notifications:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch notifications'
    });
  }
});

// Get unread count for admin
router.get('/admin/unread-count', requireAdmin, async (req, res) => {
  try {
    const count = await Notification.getUnreadCount();
    res.json({ success: true, count });
  } catch (error) {
    console.error('Error fetching unread count:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch unread count'
    });
  }
});

// Mark notification as read
router.post('/admin/:id/mark-read', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const success = await Notification.markAsRead(id);

    if (success) {
      // Emit real-time update to other admin clients
      notificationService.emitToAdmins('notification:updated', {
        id,
        isRead: true
      });

      res.json({ success: true });
    } else {
      res.status(404).json({
        success: false,
        error: 'Notification not found'
      });
    }
  } catch (error) {
    console.error('Error marking notification as read:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to mark notification as read'
    });
  }
});

// Mark all notifications as read
router.post('/admin/mark-all-read', requireAdmin, async (req, res) => {
  try {
    const count = await Notification.markAllAsRead();

    // Emit real-time update to other admin clients
    notificationService.emitToAdmins('notification:allMarkedRead');

    res.json({
      success: true,
      markedCount: count
    });
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to mark all notifications as read'
    });
  }
});

// Delete notification
router.delete('/admin/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const success = await Notification.delete(id);

    if (success) {
      // Emit real-time update to other admin clients
      notificationService.emitToAdmins('notification:deleted', { id });

      res.json({ success: true });
    } else {
      res.status(404).json({
        success: false,
        error: 'Notification not found'
      });
    }
  } catch (error) {
    console.error('Error deleting notification:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete notification'
    });
  }
});

// Create test notification (for development/testing)
router.post('/admin/test', requireAdmin, async (req, res) => {
  try {
    const {
      title = 'Test Notification',
      message = 'This is a test notification',
      type = 'info',
      category = 'system',
      priority = 'normal'
    } = req.body;

    const notification = await notificationService.createNotification({
      title,
      message,
      type,
      category,
      priority
    });

    res.json({
      success: true,
      notification
    });
  } catch (error) {
    console.error('Error creating test notification:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create test notification'
    });
  }
});

// Create multiple sample notifications
router.post('/admin/create-samples', requireAdmin, async (req, res) => {
  try {
    const sampleNotifications = [
      {
        title: 'System Started',
        message: 'StreamFlow system has been started successfully',
        type: 'success',
        category: 'system',
        priority: 'normal'
      },
      {
        title: 'High CPU Usage Detected',
        message: 'CPU usage has exceeded 80% for the last 5 minutes',
        type: 'warning',
        category: 'performance',
        priority: 'high'
      },
      {
        title: 'Stream Quality Reduced',
        message: 'Stream quality has been automatically reduced due to high server load',
        type: 'warning',
        category: 'streams',
        priority: 'normal'
      },
      {
        title: 'New User Registration',
        message: 'A new user has registered: testuser123',
        type: 'info',
        category: 'users',
        priority: 'low'
      },
      {
        title: 'Critical Memory Usage',
        message: 'Memory usage has exceeded 95% - immediate attention required',
        type: 'error',
        category: 'performance',
        priority: 'critical'
      }
    ];

    const createdNotifications = [];
    for (const notificationData of sampleNotifications) {
      const notification = await notificationService.createNotification(notificationData);
      createdNotifications.push(notification);
    }

    res.json({
      success: true,
      message: `Created ${createdNotifications.length} sample notifications`,
      notifications: createdNotifications
    });
  } catch (error) {
    console.error('Error creating sample notifications:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create sample notifications'
    });
  }
});

// Create test user notification (for development/testing)
router.post('/user/test', requireAuth, async (req, res) => {
  try {
    const {
      title = 'Test User Notification',
      message = 'This is a test notification for you',
      type = 'info',
      priority = 'normal'
    } = req.body;

    const notification = await notificationService.notifyUserSpecific(
      req.session.userId,
      title,
      message,
      type,
      priority
    );

    res.json({
      success: true,
      notification
    });
  } catch (error) {
    console.error('Error creating test user notification:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create test notification'
    });
  }
});

// Get notification statistics
router.get('/admin/stats', requireAdmin, async (req, res) => {
  try {
    const stats = await Notification.getStats();
    const unreadCount = await Notification.getUnreadCount();

    // Group stats by type, category, and priority
    const grouped = {
      byType: {},
      byCategory: {},
      byPriority: {},
      total: 0,
      unread: unreadCount
    };

    stats.forEach(stat => {
      grouped.total += stat.count;

      if (!grouped.byType[stat.type]) {
        grouped.byType[stat.type] = { total: 0, unread: 0 };
      }
      grouped.byType[stat.type].total += stat.count;
      grouped.byType[stat.type].unread += stat.unread_count;

      if (!grouped.byCategory[stat.category]) {
        grouped.byCategory[stat.category] = { total: 0, unread: 0 };
      }
      grouped.byCategory[stat.category].total += stat.count;
      grouped.byCategory[stat.category].unread += stat.unread_count;

      if (!grouped.byPriority[stat.priority]) {
        grouped.byPriority[stat.priority] = { total: 0, unread: 0 };
      }
      grouped.byPriority[stat.priority].total += stat.count;
      grouped.byPriority[stat.priority].unread += stat.unread_count;
    });

    res.json({
      success: true,
      stats: grouped
    });
  } catch (error) {
    console.error('Error fetching notification stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch notification statistics'
    });
  }
});

// Cleanup old notifications
router.post('/admin/cleanup', requireAdmin, async (req, res) => {
  try {
    const { daysToKeep = 30 } = req.body;
    const deletedCount = await Notification.cleanup(parseInt(daysToKeep));

    res.json({
      success: true,
      deletedCount,
      message: `Cleaned up ${deletedCount} old notifications`
    });
  } catch (error) {
    console.error('Error cleaning up notifications:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cleanup notifications'
    });
  }
});

// Debug endpoint - remove in production
router.get('/debug/session', async (req, res) => {
  try {
    console.log('=== SESSION DEBUG ENDPOINT ===');
    console.log('Session:', req.session);
    console.log('User ID:', req.session?.userId);

    // Check user in database
    let user = null;
    if (req.session?.userId) {
      user = await new Promise((resolve, reject) => {
        db.get('SELECT id, username, role FROM users WHERE id = ?', [req.session.userId], (err, row) => {
          if (err) reject(err);
          else resolve(row);
        });
      });
    }

    res.json({
      success: true,
      debug: true,
      session: req.session,
      user: user,
      isAuthenticated: !!req.session?.userId,
      isAdmin: user?.role === 'admin'
    });
  } catch (error) {
    console.error('Session debug error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Temporary admin endpoint without auth for debugging
router.get('/admin-noauth', async (req, res) => {
  try {
    console.log('=== ADMIN NO AUTH ENDPOINT ===');

    const {
      page = 1,
      limit = 20,
      type,
      category,
      priority,
      is_read
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);

    const options = {
      target_user_id: null, // Admin notifications only
      limit: parseInt(limit),
      offset,
      ...(type && { type }),
      ...(category && { category }),
      ...(priority && { priority }),
      ...(is_read !== undefined && { is_read: is_read === 'true' })
    };

    console.log('Query options:', options);

    const notifications = await Notification.findAll(options);
    const unreadCount = await Notification.getUnreadCount();
    const rawStats = await Notification.getStats();

    console.log('Found notifications:', notifications.length);
    console.log('Unread count:', unreadCount);
    console.log('Raw stats length:', rawStats.length);

    // Process stats into the expected format
    const stats = {
      total: 0,
      byType: {},
      byCategory: {},
      byPriority: {}
    };

    // Calculate total notifications
    rawStats.forEach(stat => {
      stats.total += stat.count;

      // Group by type
      if (!stats.byType[stat.type]) {
        stats.byType[stat.type] = { total: 0, unread: 0 };
      }
      stats.byType[stat.type].total += stat.count;
      stats.byType[stat.type].unread += stat.unread_count;

      // Group by category
      if (!stats.byCategory[stat.category]) {
        stats.byCategory[stat.category] = { total: 0, unread: 0 };
      }
      stats.byCategory[stat.category].total += stat.count;
      stats.byCategory[stat.category].unread += stat.unread_count;

      // Group by priority
      if (!stats.byPriority[stat.priority]) {
        stats.byPriority[stat.priority] = { total: 0, unread: 0 };
      }
      stats.byPriority[stat.priority].total += stat.count;
      stats.byPriority[stat.priority].unread += stat.unread_count;
    });

    console.log('Processed stats:', stats);

    res.json({
      success: true,
      notifications,
      unreadCount,
      stats,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        hasMore: notifications.length === parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error in admin-noauth endpoint:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch notifications',
      details: error.message
    });
  }
});

module.exports = router;
