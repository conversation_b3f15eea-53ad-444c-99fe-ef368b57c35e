const { db } = require('../db/database');
const { v4: uuidv4 } = require('uuid');

// Sample notifications to create
const testNotifications = [
  {
    title: 'System Started',
    message: 'StreamFlow system has been started successfully',
    type: 'success',
    category: 'system',
    priority: 'normal'
  },
  {
    title: 'High CPU Usage Detected',
    message: 'CPU usage has exceeded 80% for the last 5 minutes',
    type: 'warning',
    category: 'performance',
    priority: 'high'
  },
  {
    title: 'Stream Quality Reduced',
    message: 'Stream quality has been automatically reduced due to high server load',
    type: 'warning',
    category: 'streams',
    priority: 'normal'
  },
  {
    title: 'New User Registration',
    message: 'A new user has registered: testuser123',
    type: 'info',
    category: 'users',
    priority: 'low'
  },
  {
    title: 'Storage Quota Warning',
    message: 'User storage quota is approaching limit (85% used)',
    type: 'warning',
    category: 'system',
    priority: 'normal'
  },
  {
    title: 'Stream Error Detected',
    message: 'Stream "Live Gaming Session" encountered FFmpeg errors and was auto-stopped',
    type: 'error',
    category: 'streams',
    priority: 'high'
  },
  {
    title: 'Database Backup Completed',
    message: 'Daily database backup completed successfully',
    type: 'success',
    category: 'system',
    priority: 'low'
  },
  {
    title: 'Critical Memory Usage',
    message: 'Memory usage has exceeded 95% - immediate attention required',
    type: 'error',
    category: 'performance',
    priority: 'critical'
  },
  {
    title: 'Load Balancing Activated',
    message: 'Automatic load balancing has been activated due to high server load',
    type: 'info',
    category: 'performance',
    priority: 'normal'
  },
  {
    title: 'Subscription Expiring Soon',
    message: 'User subscription will expire in 3 days',
    type: 'warning',
    category: 'users',
    priority: 'normal'
  }
];

async function createTestNotifications() {
  console.log('Creating test notifications...');
  
  try {
    // First, check if notifications already exist
    const existingCount = await new Promise((resolve, reject) => {
      db.get('SELECT COUNT(*) as count FROM notifications', [], (err, row) => {
        if (err) reject(err);
        else resolve(row.count);
      });
    });

    if (existingCount > 0) {
      console.log(`Found ${existingCount} existing notifications. Skipping creation.`);
      return;
    }

    // Create notifications
    for (const notification of testNotifications) {
      const id = uuidv4();
      
      await new Promise((resolve, reject) => {
        db.run(`
          INSERT INTO notifications (
            id, title, message, type, category, priority,
            target_user_id, metadata, is_read, expires_at, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
        `, [
          id,
          notification.title,
          notification.message,
          notification.type,
          notification.category,
          notification.priority,
          null, // target_user_id (null for admin notifications)
          null, // metadata
          Math.random() > 0.7 ? 1 : 0, // randomly mark some as read
          null  // expires_at
        ], function(err) {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        });
      });

      console.log(`Created notification: ${notification.title}`);
    }

    console.log(`Successfully created ${testNotifications.length} test notifications!`);
    
    // Show final count
    const finalCount = await new Promise((resolve, reject) => {
      db.get('SELECT COUNT(*) as count FROM notifications', [], (err, row) => {
        if (err) reject(err);
        else resolve(row.count);
      });
    });

    console.log(`Total notifications in database: ${finalCount}`);

  } catch (error) {
    console.error('Error creating test notifications:', error);
  } finally {
    db.close();
  }
}

// Run the script
createTestNotifications();
