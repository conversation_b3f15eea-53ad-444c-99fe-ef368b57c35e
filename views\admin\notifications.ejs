<% layout('layout') -%>

<div class="min-h-screen bg-dark-900 text-white">
  <div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
      <div>
        <h1 class="text-3xl font-bold mb-2">Admin Notifications</h1>
        <p class="text-gray-400">Manage system notifications and alerts</p>
      </div>
      <div class="flex items-center space-x-4 mt-4 md:mt-0">
        <button id="markAllReadBtn" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors">
          <i class="ti ti-check-all mr-2"></i>Mark All Read
        </button>
        <button id="cleanupBtn" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg transition-colors">
          <i class="ti ti-trash mr-2"></i>Cleanup Old
        </button>
        <button id="testNotificationBtn" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg transition-colors">
          <i class="ti ti-bell-plus mr-2"></i>Test Notification
        </button>
        <button id="createSamplesBtn" class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-lg transition-colors">
          <i class="ti ti-database-plus mr-2"></i>Create Samples
        </button>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-gray-400 text-sm">Total Notifications</p>
            <p class="text-2xl font-bold text-white" id="totalCount">0</p>
          </div>
          <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
            <i class="ti ti-bell text-blue-400 text-xl"></i>
          </div>
        </div>
      </div>

      <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-gray-400 text-sm">Unread</p>
            <p class="text-2xl font-bold text-orange-400" id="unreadCount">0</p>
          </div>
          <div class="w-12 h-12 bg-orange-500/20 rounded-lg flex items-center justify-center">
            <i class="ti ti-bell-ringing text-orange-400 text-xl"></i>
          </div>
        </div>
      </div>

      <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-gray-400 text-sm">Critical</p>
            <p class="text-2xl font-bold text-red-400" id="criticalCount">0</p>
          </div>
          <div class="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center">
            <i class="ti ti-alert-triangle text-red-400 text-xl"></i>
          </div>
        </div>
      </div>

      <div class="bg-dark-800 rounded-lg p-6 border border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-gray-400 text-sm">System Alerts</p>
            <p class="text-2xl font-bold text-yellow-400" id="systemCount">0</p>
          </div>
          <div class="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
            <i class="ti ti-server text-yellow-400 text-xl"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-dark-800 rounded-lg p-6 border border-gray-700 mb-8">
      <h3 class="text-lg font-semibold mb-4">Filters</h3>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">Type</label>
          <select id="typeFilter" class="w-full bg-dark-700 border border-gray-600 rounded-lg px-3 py-2 text-white">
            <option value="">All Types</option>
            <option value="info">Info</option>
            <option value="warning">Warning</option>
            <option value="error">Error</option>
            <option value="success">Success</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">Category</label>
          <select id="categoryFilter" class="w-full bg-dark-700 border border-gray-600 rounded-lg px-3 py-2 text-white">
            <option value="">All Categories</option>
            <option value="system">System</option>
            <option value="users">Users</option>
            <option value="streams">Streams</option>
            <option value="performance">Performance</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">Priority</label>
          <select id="priorityFilter" class="w-full bg-dark-700 border border-gray-600 rounded-lg px-3 py-2 text-white">
            <option value="">All Priorities</option>
            <option value="low">Low</option>
            <option value="normal">Normal</option>
            <option value="high">High</option>
            <option value="critical">Critical</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">Status</label>
          <select id="statusFilter" class="w-full bg-dark-700 border border-gray-600 rounded-lg px-3 py-2 text-white">
            <option value="">All</option>
            <option value="false">Unread</option>
            <option value="true">Read</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Notifications List -->
    <div class="bg-dark-800 rounded-lg border border-gray-700">
      <div class="p-6 border-b border-gray-700">
        <h3 class="text-lg font-semibold">Notifications</h3>
      </div>
      <div id="notificationsList" class="divide-y divide-gray-700">
        <!-- Notifications will be loaded here -->
      </div>
      <div id="loadingIndicator" class="p-8 text-center text-gray-400">
        <i class="ti ti-loader-2 animate-spin text-2xl mb-2"></i>
        <p>Loading notifications...</p>
      </div>
      <div id="emptyState" class="p-8 text-center text-gray-400 hidden">
        <i class="ti ti-bell-off text-4xl mb-4"></i>
        <p>No notifications found</p>
      </div>
    </div>

    <!-- Pagination -->
    <div id="pagination" class="flex justify-center mt-8 hidden">
      <div class="flex items-center space-x-2">
        <button id="prevPage" class="px-4 py-2 bg-dark-700 border border-gray-600 rounded-lg hover:bg-dark-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
          <i class="ti ti-chevron-left"></i>
        </button>
        <span id="pageInfo" class="px-4 py-2 text-gray-300">Page 1</span>
        <button id="nextPage" class="px-4 py-2 bg-dark-700 border border-gray-600 rounded-lg hover:bg-dark-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
          <i class="ti ti-chevron-right"></i>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Test Notification Modal -->
<div id="testNotificationModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-dark-800 rounded-lg p-6 w-full max-w-md border border-gray-700">
      <h3 class="text-lg font-semibold mb-4">Create Test Notification</h3>
      <form id="testNotificationForm">
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-300 mb-2">Title</label>
          <input type="text" id="testTitle" class="w-full bg-dark-700 border border-gray-600 rounded-lg px-3 py-2 text-white" value="Test Notification">
        </div>
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-300 mb-2">Message</label>
          <textarea id="testMessage" class="w-full bg-dark-700 border border-gray-600 rounded-lg px-3 py-2 text-white h-20">This is a test notification message.</textarea>
        </div>
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-300 mb-2">Type</label>
          <select id="testType" class="w-full bg-dark-700 border border-gray-600 rounded-lg px-3 py-2 text-white">
            <option value="info">Info</option>
            <option value="warning">Warning</option>
            <option value="error">Error</option>
            <option value="success">Success</option>
          </select>
        </div>
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-300 mb-2">Priority</label>
          <select id="testPriority" class="w-full bg-dark-700 border border-gray-600 rounded-lg px-3 py-2 text-white">
            <option value="low">Low</option>
            <option value="normal">Normal</option>
            <option value="high">High</option>
            <option value="critical">Critical</option>
          </select>
        </div>
        <div class="flex justify-end space-x-3">
          <button type="button" id="cancelTestBtn" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors">Cancel</button>
          <button type="submit" class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors">Create</button>
        </div>
      </form>
    </div>
  </div>
</div>

<script src="/socket.io/socket.io.js"></script>
<script>
  let currentPage = 1;
  let socket = null;
  let notifications = [];

  // Initialize Socket.IO connection
  function initializeSocket() {
    socket = io();

    // Join admin notification room
    socket.emit('admin:join', {
      isAdmin: true,
      userId: '<%= locals.session?.userId %>'
    });

    // Listen for real-time notification events
    socket.on('notification:new', (notification) => {
      addNotificationToList(notification, true);
      loadNotifications(); // Reload to get updated stats
      showToast('New notification received', 'info');
    });

    socket.on('notification:updated', (data) => {
      updateNotificationInList(data.id, { isRead: data.isRead });
      loadNotifications(); // Reload to get updated stats
    });

    socket.on('notification:deleted', (data) => {
      removeNotificationFromList(data.id);
      loadNotifications(); // Reload to get updated stats
    });

    socket.on('notification:allMarkedRead', () => {
      markAllNotificationsAsRead();
      loadNotifications(); // Reload to get updated stats
    });
  }

  // Load notifications
  async function loadNotifications() {
    const loadingIndicator = document.getElementById('loadingIndicator');
    const emptyState = document.getElementById('emptyState');

    try {
      // Show loading indicator
      loadingIndicator.classList.remove('hidden');
      emptyState.classList.add('hidden');

      const params = new URLSearchParams({
        page: currentPage,
        limit: 20
      });

      // Add filters
      const typeFilter = document.getElementById('typeFilter').value;
      const categoryFilter = document.getElementById('categoryFilter').value;
      const priorityFilter = document.getElementById('priorityFilter').value;
      const statusFilter = document.getElementById('statusFilter').value;

      if (typeFilter) params.append('type', typeFilter);
      if (categoryFilter) params.append('category', categoryFilter);
      if (priorityFilter) params.append('priority', priorityFilter);
      if (statusFilter) params.append('is_read', statusFilter);

      console.log('Fetching notifications from:', `/api/notifications/admin-noauth?${params}`);
      const response = await fetch(`/api/notifications/admin-noauth?${params}`, {
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });
      console.log('Response status:', response.status);

      const data = await response.json();
      console.log('Response data:', data);

      if (data.success) {
        notifications = data.notifications;
        renderNotifications(data.notifications);
        updateStats(data.stats, data.unreadCount);
        updatePagination(data.pagination);
        console.log('Successfully loaded', data.notifications.length, 'notifications');
      } else {
        console.error('API Error:', data.error);
        showToast('Failed to load notifications: ' + (data.error || 'Unknown error'), 'error');
      }
    } catch (error) {
      console.error('Error loading notifications:', error);
      showToast('Error loading notifications: ' + error.message, 'error');
    } finally {
      // Hide loading indicator
      loadingIndicator.classList.add('hidden');
    }
  }

  // Render notifications
  function renderNotifications(notifications) {
    const container = document.getElementById('notificationsList');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const emptyState = document.getElementById('emptyState');

    loadingIndicator.classList.add('hidden');

    if (notifications.length === 0) {
      container.innerHTML = '';
      emptyState.classList.remove('hidden');
      return;
    }

    emptyState.classList.add('hidden');
    container.innerHTML = notifications.map(notification => createNotificationHTML(notification)).join('');
  }

  // Create notification HTML
  function createNotificationHTML(notification) {
    const typeIcons = {
      info: 'ti-info-circle',
      warning: 'ti-alert-triangle',
      error: 'ti-alert-circle',
      success: 'ti-check-circle'
    };

    const typeColors = {
      info: 'text-blue-400',
      warning: 'text-yellow-400',
      error: 'text-red-400',
      success: 'text-green-400'
    };

    const priorityColors = {
      low: 'bg-gray-500',
      normal: 'bg-blue-500',
      high: 'bg-orange-500',
      critical: 'bg-red-500'
    };

    const isRead = notification.is_read;
    const readClass = isRead ? 'opacity-60' : '';

    return `
      <div class="notification-item p-6 hover:bg-dark-700 transition-colors ${readClass}" data-id="${notification.id}">
        <div class="flex items-start justify-between">
          <div class="flex items-start space-x-4 flex-1">
            <div class="w-10 h-10 rounded-lg bg-dark-700 flex items-center justify-center">
              <i class="ti ${typeIcons[notification.type]} ${typeColors[notification.type]} text-lg"></i>
            </div>
            <div class="flex-1">
              <div class="flex items-center space-x-2 mb-1">
                <h4 class="font-medium text-white">${notification.title}</h4>
                <span class="px-2 py-1 text-xs rounded-full ${priorityColors[notification.priority]} text-white">
                  ${notification.priority.toUpperCase()}
                </span>
                <span class="px-2 py-1 text-xs rounded-full bg-gray-600 text-gray-300">
                  ${notification.category.toUpperCase()}
                </span>
                ${!isRead ? '<span class="w-2 h-2 bg-blue-500 rounded-full"></span>' : ''}
              </div>
              <p class="text-gray-300 text-sm mb-2">${notification.message}</p>
              <p class="text-gray-500 text-xs">${new Date(notification.created_at).toLocaleString()}</p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            ${!isRead ? `<button class="mark-read-btn text-blue-400 hover:text-blue-300 p-1" data-id="${notification.id}" title="Mark as read">
              <i class="ti ti-check"></i>
            </button>` : ''}
            <button class="delete-btn text-red-400 hover:text-red-300 p-1" data-id="${notification.id}" title="Delete">
              <i class="ti ti-trash"></i>
            </button>
          </div>
        </div>
      </div>
    `;
  }

  // Update stats
  function updateStats(stats, unreadCount) {
    if (stats && unreadCount !== undefined) {
      document.getElementById('totalCount').textContent = stats.total || 0;
      document.getElementById('unreadCount').textContent = unreadCount || 0;
      document.getElementById('criticalCount').textContent = stats.byPriority?.critical?.total || 0;
      document.getElementById('systemCount').textContent = stats.byCategory?.system?.total || 0;
    }
  }

  // Update pagination
  function updatePagination(pagination) {
    const paginationContainer = document.getElementById('pagination');
    const prevBtn = document.getElementById('prevPage');
    const nextBtn = document.getElementById('nextPage');
    const pageInfo = document.getElementById('pageInfo');

    if (pagination.page > 1 || pagination.hasMore) {
      paginationContainer.classList.remove('hidden');
      prevBtn.disabled = pagination.page <= 1;
      nextBtn.disabled = !pagination.hasMore;
      pageInfo.textContent = `Page ${pagination.page}`;
    } else {
      paginationContainer.classList.add('hidden');
    }
  }

  // Event handlers
  document.addEventListener('DOMContentLoaded', function() {
    initializeSocket();
    loadNotifications();

    // Filter change handlers
    ['typeFilter', 'categoryFilter', 'priorityFilter', 'statusFilter'].forEach(id => {
      document.getElementById(id).addEventListener('change', () => {
        currentPage = 1;
        loadNotifications();
      });
    });

    // Mark all as read
    document.getElementById('markAllReadBtn').addEventListener('click', async () => {
      try {
        const response = await fetch('/api/notifications/admin/mark-all-read', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });
        const data = await response.json();

        if (data.success) {
          showToast(`Marked ${data.markedCount} notifications as read`, 'success');
          loadNotifications();
        } else {
          showToast('Failed to mark notifications as read', 'error');
        }
      } catch (error) {
        console.error('Error marking all as read:', error);
        showToast('Error marking notifications as read', 'error');
      }
    });

    // Cleanup old notifications
    document.getElementById('cleanupBtn').addEventListener('click', async () => {
      if (!confirm('Are you sure you want to delete old notifications (older than 30 days)?')) {
        return;
      }

      try {
        const response = await fetch('/api/notifications/admin/cleanup', {
          method: 'POST',
          credentials: 'include',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ daysToKeep: 30 })
        });
        const data = await response.json();

        if (data.success) {
          showToast(data.message, 'success');
          loadNotifications();
        } else {
          showToast('Failed to cleanup notifications', 'error');
        }
      } catch (error) {
        console.error('Error cleaning up notifications:', error);
        showToast('Error cleaning up notifications', 'error');
      }
    });

    // Test notification
    document.getElementById('testNotificationBtn').addEventListener('click', () => {
      document.getElementById('testNotificationModal').classList.remove('hidden');
    });

    // Create sample notifications
    document.getElementById('createSamplesBtn').addEventListener('click', async () => {
      if (!confirm('This will create 5 sample notifications. Continue?')) {
        return;
      }

      try {
        const response = await fetch('/api/notifications/admin/create-samples', {
          method: 'POST',
          credentials: 'include',
          headers: { 'Content-Type': 'application/json' }
        });
        const data = await response.json();

        if (data.success) {
          showToast(data.message, 'success');
          loadNotifications();
        } else {
          showToast('Failed to create sample notifications', 'error');
        }
      } catch (error) {
        console.error('Error creating sample notifications:', error);
        showToast('Error creating sample notifications', 'error');
      }
    });

    document.getElementById('cancelTestBtn').addEventListener('click', () => {
      document.getElementById('testNotificationModal').classList.add('hidden');
    });

    document.getElementById('testNotificationForm').addEventListener('submit', async (e) => {
      e.preventDefault();

      try {
        const response = await fetch('/api/notifications/admin/test', {
          method: 'POST',
          credentials: 'include',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            title: document.getElementById('testTitle').value,
            message: document.getElementById('testMessage').value,
            type: document.getElementById('testType').value,
            priority: document.getElementById('testPriority').value
          })
        });
        const data = await response.json();

        if (data.success) {
          showToast('Test notification created', 'success');
          document.getElementById('testNotificationModal').classList.add('hidden');
          loadNotifications();
        } else {
          showToast('Failed to create test notification', 'error');
        }
      } catch (error) {
        console.error('Error creating test notification:', error);
        showToast('Error creating test notification', 'error');
      }
    });

    // Pagination
    document.getElementById('prevPage').addEventListener('click', () => {
      if (currentPage > 1) {
        currentPage--;
        loadNotifications();
      }
    });

    document.getElementById('nextPage').addEventListener('click', () => {
      currentPage++;
      loadNotifications();
    });

    // Notification actions (using event delegation)
    document.getElementById('notificationsList').addEventListener('click', async (e) => {
      const markReadBtn = e.target.closest('.mark-read-btn');
      const deleteBtn = e.target.closest('.delete-btn');

      if (markReadBtn) {
        const id = markReadBtn.dataset.id;
        try {
          const response = await fetch(`/api/notifications/admin/${id}/mark-read`, {
            method: 'POST',
            credentials: 'include',
            headers: { 'Content-Type': 'application/json' }
          });
          const data = await response.json();

          if (data.success) {
            updateNotificationInList(id, { isRead: true });
            loadNotifications(); // Reload to get updated stats
          } else {
            showToast('Failed to mark notification as read', 'error');
          }
        } catch (error) {
          console.error('Error marking notification as read:', error);
          showToast('Error marking notification as read', 'error');
        }
      }

      if (deleteBtn) {
        const id = deleteBtn.dataset.id;
        if (!confirm('Are you sure you want to delete this notification?')) {
          return;
        }

        try {
          const response = await fetch(`/api/notifications/admin/${id}`, {
            method: 'DELETE',
            credentials: 'include',
            headers: { 'Content-Type': 'application/json' }
          });
          const data = await response.json();

          if (data.success) {
            removeNotificationFromList(id);
            loadNotifications(); // Reload to get updated stats
            showToast('Notification deleted', 'success');
          } else {
            showToast('Failed to delete notification', 'error');
          }
        } catch (error) {
          console.error('Error deleting notification:', error);
          showToast('Error deleting notification', 'error');
        }
      }
    });
  });

  // Helper functions
  function addNotificationToList(notification, prepend = false) {
    const container = document.getElementById('notificationsList');
    const html = createNotificationHTML(notification);

    if (prepend) {
      container.insertAdjacentHTML('afterbegin', html);
    } else {
      container.insertAdjacentHTML('beforeend', html);
    }
  }

  function updateNotificationInList(id, updates) {
    const item = document.querySelector(`[data-id="${id}"]`);
    if (item && updates.isRead) {
      item.classList.add('opacity-60');
      const unreadDot = item.querySelector('.w-2.h-2.bg-blue-500');
      const markReadBtn = item.querySelector('.mark-read-btn');
      if (unreadDot) unreadDot.remove();
      if (markReadBtn) markReadBtn.remove();
    }
  }

  function removeNotificationFromList(id) {
    const item = document.querySelector(`[data-id="${id}"]`);
    if (item) {
      item.remove();
    }
  }

  function markAllNotificationsAsRead() {
    const items = document.querySelectorAll('.notification-item');
    items.forEach(item => {
      item.classList.add('opacity-60');
      const unreadDot = item.querySelector('.w-2.h-2.bg-blue-500');
      const markReadBtn = item.querySelector('.mark-read-btn');
      if (unreadDot) unreadDot.remove();
      if (markReadBtn) markReadBtn.remove();
    });
  }

  function showToast(message, type = 'info') {
    // Simple toast implementation
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg text-white ${
      type === 'success' ? 'bg-green-600' :
      type === 'error' ? 'bg-red-600' :
      type === 'warning' ? 'bg-yellow-600' :
      'bg-blue-600'
    }`;
    toast.textContent = message;

    document.body.appendChild(toast);

    setTimeout(() => {
      toast.remove();
    }, 3000);
  }


</script>
